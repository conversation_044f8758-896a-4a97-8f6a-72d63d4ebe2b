# Shannet Corporate Events

## Project Overview

Shannet Events is a premier corporate event management company specializing in creating unforgettable experiences for conferences, galas, weddings, and business events. This is the official website showcasing our portfolio and services.

## Development Setup

To run this project locally, you'll need Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository
git clone https://github.com/tmx34/shannet-corporate-events.git

# Step 2: Navigate to the project directory
cd shannet-corporate-events

# Step 3: Install the necessary dependencies
npm install

# Step 4: Start the development server
npm run dev
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Technologies Used

This project is built with modern web technologies:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library
- **shadcn/ui** - Modern component library
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Hook Form** - Form handling
- **Lucide React** - Icon library

## Features

- Responsive design optimized for all devices
- Modern UI with smooth animations
- Image gallery with lazy loading
- Contact form with validation
- SEO optimized
- Fast loading performance

## Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── lib/           # Utility functions
├── hooks/         # Custom React hooks
└── main.tsx       # Application entry point
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary and confidential. All rights reserved by Shannet Events.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
