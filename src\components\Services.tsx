
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, Calendar, Book, Heart, HandHeart } from "lucide-react";

const services = [
  {
    icon: <Users className="w-12 h-12 text-shannet-yellow" />,
    title: "Corporate Events",
    description: "Professional conferences and seminars with comprehensive planning, venue management, and technical support."
  },
  {
    icon: <Calendar className="w-12 h-12 text-shannet-yellow" />,
    title: "Business Galas & Awards",
    description: "Elegant corporate galas, award ceremonies, and recognition events that celebrate your achievements."
  },
  {
    icon: <Book className="w-12 h-12 text-shannet-yellow" />,
    title: "Team Building Events",
    description: "Engaging team building activities and corporate retreats designed to strengthen your organization."
  },
  {
    icon: <Users className="w-12 h-12 text-shannet-yellow" />,
    title: "Product Launches",
    description: "Impactful product launch events that create buzz and drive engagement with your target audience."
  },
  {
    icon: <HandHeart className="w-12 h-12 text-shannet-yellow" />,
    title: "Introductions",
    description: "Elegant introduction ceremonies and social events that create meaningful connections and lasting impressions."
  },
  {
    icon: <Heart className="w-12 h-12 text-shannet-yellow" />,
    title: "Weddings",
    description: "Beautiful wedding celebrations with comprehensive planning, coordination, and flawless execution for your special day."
  }
];

const Services = () => {
  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-shannet-navy mb-6">
            Our Corporate Services
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive event management solutions tailored for the corporate world
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-0 shadow-lg animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>
                <CardTitle className="text-xl font-bold text-shannet-navy group-hover:text-shannet-yellow transition-colors duration-300">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 text-center leading-relaxed">
                  {service.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Service Features */}
        <div className="mt-16 bg-white rounded-2xl shadow-lg p-8 animate-fade-in">
          <h3 className="text-2xl font-bold text-shannet-navy mb-8 text-center">
            Why Choose Shannet Events?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-shannet-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-shannet-navy">1</span>
              </div>
              <h4 className="font-semibold text-shannet-navy mb-2">Expert Planning</h4>
              <p className="text-gray-600 text-sm">Detailed planning and coordination from concept to execution</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-shannet-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-shannet-navy">2</span>
              </div>
              <h4 className="font-semibold text-shannet-navy mb-2">Premium Venues</h4>
              <p className="text-gray-600 text-sm">Access to exclusive corporate venues and facilities</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-shannet-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-shannet-navy">3</span>
              </div>
              <h4 className="font-semibold text-shannet-navy mb-2">Full Support</h4>
              <p className="text-gray-600 text-sm">Dedicated event managers and on-site support teams</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-shannet-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-shannet-navy">4</span>
              </div>
              <h4 className="font-semibold text-shannet-navy mb-2">Guaranteed Success</h4>
              <p className="text-gray-600 text-sm">Proven track record of successful corporate events</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
