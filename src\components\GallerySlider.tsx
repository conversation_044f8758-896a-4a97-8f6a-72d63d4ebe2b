
import { useState } from "react";
import Gallery from "./Gallery";

const GallerySlider = () => {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Gallery images from local assets
  const images = [
    "/images/shannet3.jpg",
    "/images/shannet5.jpg",
    "/images/shannetC.jpg",
    "/images/shannetC1.jpg",
    "/images/shannetE1.jpg",
    "/images/shannetI.jpg",
    "/images/shannetP.jpg",
    "/images/shannetP1.jpg",
    "/images/shannetP2.jpg",
    "/images/shannetS.jpg",
    "/images/shannetw.jpg",
  ];

  // Duplicate images for infinite scroll effect
  const duplicatedImages = [...images, ...images];

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index % images.length);
    setIsGalleryOpen(true);
  };

  return (
    <>
      <div className="w-full overflow-hidden bg-gradient-to-r from-shannet-navy/20 to-transparent py-4 md:py-8">
        <div className="flex animate-scroll-infinite">
          {duplicatedImages.map((image, index) => (
            <div
              key={index}
              className="flex-shrink-0 mx-1 md:mx-2 cursor-pointer group"
              onClick={() => handleImageClick(index)}
            >
              <div className="relative overflow-hidden rounded-lg shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:z-10 group-hover:shadow-2xl">
                <img
                  src={image}
                  alt={`Gallery image ${(index % images.length) + 1}`}
                  className="w-32 h-20 sm:w-40 sm:h-24 md:w-48 md:h-32 object-cover transition-all duration-300 group-hover:brightness-110"
                  loading="lazy"
                  decoding="async"
                />
                <div className="absolute inset-0 bg-shannet-yellow/0 group-hover:bg-shannet-yellow/20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-1.5 md:p-2">
                    <svg className="w-4 h-4 md:w-6 md:h-6 text-shannet-navy" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Gallery
        isOpen={isGalleryOpen}
        onClose={() => setIsGalleryOpen(false)}
        category="Event Gallery"
        images={images}
        initialIndex={selectedImageIndex}
      />

      <style dangerouslySetInnerHTML={{
        __html: `
        @keyframes scroll-infinite {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-scroll-infinite {
          animation: scroll-infinite 20s linear infinite;
        }
        
        .animate-scroll-infinite:hover {
          animation-play-state: paused;
        }
        
        @media (max-width: 768px) {
          .animate-scroll-infinite {
            animation: scroll-infinite 15s linear infinite;
          }
        }
        `
      }} />
    </>
  );
};

export default GallerySlider;
