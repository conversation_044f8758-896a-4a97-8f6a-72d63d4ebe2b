
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 215 25% 15%;
    --primary-foreground: 55 100% 96%;

    --secondary: 55 100% 96%;
    --secondary-foreground: 215 25% 15%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 55 100% 85%;
    --accent-foreground: 215 25% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 55 100% 48%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 25% 10%;
    --foreground: 55 100% 96%;

    --card: 215 25% 10%;
    --card-foreground: 55 100% 96%;

    --popover: 215 25% 10%;
    --popover-foreground: 55 100% 96%;

    --primary: 55 100% 48%;
    --primary-foreground: 215 25% 15%;

    --secondary: 215 25% 15%;
    --secondary-foreground: 55 100% 96%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 215 25% 20%;
    --accent-foreground: 55 100% 96%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 55 100% 48%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-playfair;
  }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #F5D700;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #D4BB00;
}
