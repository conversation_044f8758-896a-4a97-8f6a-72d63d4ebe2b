
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Users, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Booking = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    company: "",
    eventType: "",
    eventDate: "",
    guestCount: "",
    budget: "",
    message: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    
    toast({
      title: "Booking Request Submitted!",
      description: "Thank you for your interest. Our team will contact you within 24 hours to discuss your event requirements.",
    });

    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      company: "",
      eventType: "",
      eventDate: "",
      guestCount: "",
      budget: "",
      message: ""
    });
  };

  return (
    <section id="booking" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-shannet-navy mb-6">
            Book Your Event
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Ready to create an exceptional corporate event? Let's discuss your vision and requirements.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Information */}
          <div className="space-y-8 animate-slide-up">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-shannet-navy flex items-center gap-3">
                  <User className="w-6 h-6 text-shannet-yellow" />
                  Get In Touch
                </CardTitle>
                <CardDescription>
                  Speak directly with our event planning experts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-semibold text-shannet-navy mb-2">Phone</h4>
                  <p className="text-gray-600">+256 (753) 010 020</p>
                </div>
                <div>
                  <h4 className="font-semibold text-shannet-navy mb-2">Email</h4>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
                <div>
                  <h4 className="font-semibold text-shannet-navy mb-2">Office Hours</h4>
                  <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM</p>
                  <p className="text-gray-600">Saturday: 10:00 AM - 4:00 PM</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-shannet-navy flex items-center gap-3">
                  <Calendar className="w-6 h-6 text-shannet-yellow" />
                  Planning Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-shannet-yellow rounded-full flex items-center justify-center font-bold text-shannet-navy text-sm">
                    1
                  </div>
                  <div>
                    <h5 className="font-semibold text-shannet-navy">Initial Consultation</h5>
                    <p className="text-gray-600 text-sm">We discuss your vision, requirements, and budget</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-shannet-yellow rounded-full flex items-center justify-center font-bold text-shannet-navy text-sm">
                    2
                  </div>
                  <div>
                    <h5 className="font-semibold text-shannet-navy">Proposal & Planning</h5>
                    <p className="text-gray-600 text-sm">Detailed event proposal with timeline and logistics</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-shannet-yellow rounded-full flex items-center justify-center font-bold text-shannet-navy text-sm">
                    3
                  </div>
                  <div>
                    <h5 className="font-semibold text-shannet-navy">Event Execution</h5>
                    <p className="text-gray-600 text-sm">Seamless coordination and on-site management</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          {/* Booking Form
          <Card className="border-0 shadow-lg animate-slide-up">
            <CardHeader>
              <CardTitle className="text-2xl text-shannet-navy flex items-center gap-3">
                <Users className="w-6 h-6 text-shannet-yellow" />
                Event Booking Form
              </CardTitle>
              <CardDescription>
                Fill out the details below and we'll get back to you shortly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Full Name *
                    </label>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Email *
                    </label>
                    <Input
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Phone Number *
                    </label>
                    <Input
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                      className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Company Name
                    </label>
                    <Input
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Event Type *
                    </label>
                    <Select onValueChange={(value) => handleSelectChange("eventType", value)}>
                      <SelectTrigger className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow">
                        <SelectValue placeholder="Select event type" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                        <SelectItem value="conference">Corporate Conference</SelectItem>
                        <SelectItem value="gala">Business Gala</SelectItem>
                        <SelectItem value="team-building">Team Building</SelectItem>
                        <SelectItem value="product-launch">Product Launch</SelectItem>
                        <SelectItem value="trade-show">Trade Show</SelectItem>
                        <SelectItem value="meeting">Board Meeting/AGM</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Preferred Date
                    </label>
                    <Input
                      name="eventDate"
                      type="date"
                      value={formData.eventDate}
                      onChange={handleInputChange}
                      className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Expected Guest Count
                    </label>
                    <Select onValueChange={(value) => handleSelectChange("guestCount", value)}>
                      <SelectTrigger className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow">
                        <SelectValue placeholder="Select guest count" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                        <SelectItem value="1-50">1-50 guests</SelectItem>
                        <SelectItem value="51-100">51-100 guests</SelectItem>
                        <SelectItem value="101-250">101-250 guests</SelectItem>
                        <SelectItem value="251-500">251-500 guests</SelectItem>
                        <SelectItem value="500+">500+ guests</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-shannet-navy mb-2">
                      Estimated Budget
                    </label>
                    <Select onValueChange={(value) => handleSelectChange("budget", value)}>
                      <SelectTrigger className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow">
                        <SelectValue placeholder="Select budget range" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                        <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                        <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                        <SelectItem value="50k-100k">$50,000 - $100,000</SelectItem>
                        <SelectItem value="100k+">$100,000+</SelectItem>
                        <SelectItem value="discuss">Prefer to discuss</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-shannet-navy mb-2">
                    Event Details & Special Requirements
                  </label>
                  <Textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Tell us about your event vision, specific requirements, or any questions you have..."
                    className="border-gray-300 focus:border-shannet-yellow focus:ring-shannet-yellow"
                  />
                </div>

                <Button 
                  type="submit"
                  className="w-full bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-bold py-3 text-lg rounded-lg transition-all duration-300 transform hover:scale-105"
                >
                  Submit Booking Request
                </Button>
              </form>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </section>
  );
};

export default Booking;
