
import { Button } from "@/components/ui/button";
import GallerySlider from "./GallerySlider";

const Hero = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen flex flex-col bg-gradient-to-br from-shannet-navy via-shannet-navy-light to-shannet-navy-dark relative overflow-hidden pt-16 sm:pt-18 md:pt-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-4 md:top-20 md:left-20 w-16 h-16 md:w-32 md:h-32 bg-shannet-yellow rounded-full blur-2xl md:blur-3xl"></div>
        <div className="absolute bottom-10 right-4 md:bottom-20 md:right-20 w-24 h-24 md:w-48 md:h-48 bg-shannet-yellow rounded-full blur-2xl md:blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 md:left-1/3 w-12 h-12 md:w-24 md:h-24 bg-shannet-yellow rounded-full blur-xl md:blur-2xl"></div>
        <div className="absolute top-10 left-4 md:top-20 md:left-20 w-16 h-16 md:w-32 md:h-32 bg-shannet-yellow rounded-full blur-2xl md:blur-3xl"></div>
        <div className="absolute bottom-10 right-4 md:bottom-20 md:right-20 w-24 h-24 md:w-48 md:h-48 bg-shannet-yellow rounded-full blur-2xl md:blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 md:left-1/3 w-12 h-12 md:w-24 md:h-24 bg-shannet-yellow rounded-full blur-xl md:blur-2xl"></div>
      </div>

      {/* Main Hero Content */}
      <div className="flex-1 flex items-center justify-center px-4 py-4 sm:py-6 md:py-8">
        <div className="container mx-auto relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center">
            {/* Left Side - Text Content */}
            <div className="text-center lg:text-left animate-fade-in order-2 lg:order-1">
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
                Premier Corporate
                <span className="block text-shannet-yellow">Event Management</span>
              </h1>

              <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-shannet-yellow mb-2 sm:mb-3 md:mb-4 font-semibold italic">
                "Create memories that last forever"
              </p>

              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-200 mb-4 sm:mb-6 md:mb-8 max-w-lg mx-auto lg:mx-0 leading-relaxed">
                We prioritize your satisfaction with exceptional corporate events that leave lasting impressions
              </p>
              
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 justify-center lg:justify-start animate-slide-up">
                <Button
                  onClick={() => scrollToSection('booking')}
                  size="lg"
                  className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-bold px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4 text-sm sm:text-base md:text-lg rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg w-full sm:w-auto"
                >
                  Plan Your Event
                </Button>
                <Button
                  onClick={() => scrollToSection('portfolio')}
                  variant="outline"
                  size="lg"
                  className="border-2 border-shannet-yellow text-shannet-yellow hover:bg-shannet-yellow hover:text-shannet-navy font-semibold px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4 text-sm sm:text-base md:text-lg rounded-lg transition-all duration-300 transform hover:scale-105 w-full sm:w-auto"
                >
                  View Our Work
                </Button>
              </div>

              {/* Stats Section */}
              <div className="mt-6 sm:mt-8 md:mt-12 grid grid-cols-3 gap-2 sm:gap-4 md:gap-6 animate-slide-up">
                <div className="text-center lg:text-left">
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-shannet-yellow mb-0.5 sm:mb-1">100+</h3>
                  <p className="text-gray-300 text-xs sm:text-sm">Events Delivered</p>
                </div>
                <div className="text-center lg:text-left">
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-shannet-yellow mb-0.5 sm:mb-1">98%</h3>
                  <p className="text-gray-300 text-xs sm:text-sm">Client Satisfaction</p>
                </div>
                <div className="text-center lg:text-left">
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-shannet-yellow mb-0.5 sm:mb-1">10+</h3>
                  <p className="text-gray-300 text-xs sm:text-sm">Years Experience</p>
                </div>
              </div>
            </div>

            {/* Right Side - Animated Event SVG */}
            <div className="flex justify-center lg:justify-end animate-fade-in order-1 lg:order-2 mb-4 sm:mb-6 lg:mb-0">
              <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
                <svg viewBox="0 0 400 400" className="w-full h-auto" xmlns="http://www.w3.org/2000/svg">
                  {/* Definitions for Reusable Gradients */}
                  <defs>
                    <radialGradient id="spotlight" cx="50%" cy="30%" r="50%">
                      <stop offset="0%" style={{ stopColor: '#F5D700', stopOpacity: 0.3 }} />
                      <stop offset="100%" style={{ stopColor: '#F5D700', stopOpacity: 0 }} />
                    </radialGradient>
                  </defs>
            
                  {/* Event Stage/Platform */}
                  <rect
                    x="50"
                    y="280"
                    width="300"
                    height="20"
                    fill="#F5D700"
                    className="animate-pulse"
                  />
            
                  {/* Podium */}
                  <rect
                    x="180"
                    y="240"
                    width="40"
                    height="40"
                    fill="#334155"
                    rx="4"
                  />
            
                  {/* Speaker/Presenter */}
                  <g className="animate-bounce" style={{ animationDuration: '2s' }}>
                    <circle cx="200" cy="220" r="15" fill="#F5D700" />
                    <rect x="190" y="235" width="20" height="30" fill="#334155" rx="10" />
                  </g>
            
                  {/* Audience Seats */}
                  <g className="animate-pulse" style={{ animationDelay: '0.5s' }}>
                    {Array.from({ length: 6 }, (_, row) =>
                      Array.from({ length: 8 }, (_, seat) => (
                        <circle
                          key={`seat-${row}-${seat}`}
                          cx={70 + seat * 35}
                          cy={320 + row * 25}
                          r="8"
                          fill={Math.random() > 0.3 ? '#1E293B' : '#334155'}
                          opacity="0.8"
                        />
                      ))
                    )}
                  </g>
            
                  {/* Spotlight Effect */}
                  <ellipse
                    cx="200"
                    cy="150"
                    rx="80"
                    ry="40"
                    fill="url(#spotlight)"
                    className="animate-pulse"
                    style={{ animationDuration: '3s' }}
                  />
            
                  {/* Decorative Elements */}
                  <g className="animate-bounce" style={{ animationDelay: '1s', animationDuration: '3s' }}>
                    <circle cx="80" cy="80" r="4" fill="#F5D700" opacity="0.6" />
                    <circle cx="320" cy="100" r="6" fill="#F5D700" opacity="0.8" />
                    <circle cx="350" cy="60" r="3" fill="#F5D700" opacity="0.4" />
                  </g>
            
                  {/* Event Banner */}
                  <g>
                    <rect
                      x="75"
                      y="40"
                      width="260"
                      height="30"
                      fill="#1E293B"
                      rx="15"
                      className="animate-pulse"
                      style={{ animationDelay: '1.5s' }}
                    />
                    <text
                      x="200"
                      y="58"
                      textAnchor="middle"
                      fill="#F5D700"
                      fontSize="12"
                      fontWeight="bold"
                    >
                      CONGRATULATIONS SHANTAL!
                    </text>
                  </g>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Gallery Slider at the bottom */}
      <GallerySlider />
    </section>
  );
};

export default Hero;
