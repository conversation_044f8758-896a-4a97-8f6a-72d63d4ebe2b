
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
      <div className="container mx-auto px-4 py-3 md:py-4">
      <div className="container mx-auto px-4 py-3 md:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <img 
              src="/logo.png" 
              alt="Shannet Events Logo" 
              className="h-10 md:h-12 w-auto"
              className="h-10 md:h-12 w-auto"
            />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
            <button 
              onClick={() => scrollToSection('home')}
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
            >
              Home
            </button>
            <button 
              onClick={() => scrollToSection('services')}
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
            >
              Services
            </button>
            <button 
              onClick={() => scrollToSection('portfolio')}
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
              className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-sm lg:text-base"
            >
              Portfolio
            </button>
            <Button 
              onClick={() => scrollToSection('booking')}
              className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-semibold px-4 lg:px-6 py-2 text-sm lg:text-base rounded-lg transition-all duration-300 transform hover:scale-105"
              className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-semibold px-4 lg:px-6 py-2 text-sm lg:text-base rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Book Now
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden p-2 text-shannet-navy hover:bg-gray-100 rounded-lg transition-colors"
            className="md:hidden p-2 text-shannet-navy hover:bg-gray-100 rounded-lg transition-colors"
            onClick={toggleMenu}
            aria-label="Toggle mobile menu"
            aria-label="Toggle mobile menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 py-4 border-t border-gray-200 animate-fade-in">
            <div className="flex flex-col space-y-4">
              <button 
                onClick={() => scrollToSection('home')}
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
              >
                Home
              </button>
              <button 
                onClick={() => scrollToSection('services')}
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
              >
                Services
              </button>
              <button 
                onClick={() => scrollToSection('portfolio')}
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
                className="text-shannet-navy hover:text-shannet-yellow transition-colors duration-300 font-medium text-left py-2 px-2 hover:bg-gray-50 rounded-lg"
              >
                Portfolio
              </button>
              <Button 
                onClick={() => scrollToSection('booking')}
                className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-semibold px-6 py-3 text-base rounded-lg w-full mt-2"
                className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-semibold px-6 py-3 text-base rounded-lg w-full mt-2"
              >
                Book Now
              </Button>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
