
const Footer = () => {
  return (
    <footer id="contact" className="bg-shannet-navy text-white py-12 md:py-16">
    <footer id="contact" className="bg-shannet-navy text-white py-12 md:py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="sm:col-span-2 lg:col-span-2">
          <div className="sm:col-span-2 lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <img 
                src="/logo.png" 
                alt="Shannet Events Logo" 
                className="h-10 md:h-12 w-auto"
                className="h-10 md:h-12 w-auto"
              />
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md text-sm md:text-base">
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md text-sm md:text-base">
              Premier corporate event management company dedicated to creating exceptional experiences that prioritize your satisfaction and leave lasting impressions.
            </p>
            <div className="flex flex-wrap gap-6 items-center">
              <a 
                href="https://www.tiktok.com/@shannet_events5" 
                target="_blank" 
                rel="noopener noreferrer"
                className="group text-gray-300 hover:text-shannet-yellow transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5 group-hover:opacity-80 transition-opacity duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                  </svg>
                  <span className="text-sm md:text-base">TikTok</span>
                </div>
              </a>

              <a 
                href="https://twitter.com/eventsshannet" 
                target="_blank" 
                rel="noopener noreferrer"
                className="group text-gray-300 hover:text-shannet-yellow transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5 group-hover:opacity-80 transition-opacity duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                  <span className="text-sm md:text-base">Twitter</span>
                </div>
              </a>

              <a 
                href="https://www.facebook.com/Shannetevents/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="group text-gray-300 hover:text-shannet-yellow transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5 group-hover:opacity-80 transition-opacity duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.675 0H1.325C.593 0 0 .593 0 1.325v21.351C0 23.407.593 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116c.73 0 1.323-.593 1.323-1.325V1.325C24 .593 23.407 0 22.675 0z"/>
                  </svg>
                  <span className="text-sm md:text-base">Facebook</span>
                </div>
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg md:text-xl font-bold text-shannet-yellow mb-4 md:mb-6">Services</h4>
            <ul className="space-y-2 md:space-y-3">
              <li className="flex items-center text-gray-300 hover:text-white transition-colors duration-300 text-sm md:text-base group">
                <span>Corporate Events</span>
                <span className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">→</span>
              </li>
              <li className="flex items-center text-gray-300 hover:text-white transition-colors duration-300 text-sm md:text-base group">
                <span>Weddings</span>
                <span className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">→</span>
              </li>
              <li className="flex items-center text-gray-300 hover:text-white transition-colors duration-300 text-sm md:text-base group">
                <span>Introductions (Nikah, Kukyala, Kuhingira)</span>
                <span className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">→</span>
              </li>
              <li className="flex items-center text-gray-300 hover:text-white transition-colors duration-300 text-sm md:text-base group">
                <span>Proposals</span>
                <span className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">→</span>
              </li>
              <li className="flex items-center text-gray-300 hover:text-white transition-colors duration-300 text-sm md:text-base group">
                <span>Bridal Showers</span>
                <span className="ml-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">→</span>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg md:text-xl font-bold text-shannet-yellow mb-4 md:mb-6">Contact</h4>
            <div className="space-y-3 text-sm md:text-base">
            <h4 className="text-lg md:text-xl font-bold text-shannet-yellow mb-4 md:mb-6">Contact</h4>
            <div className="space-y-3 text-sm md:text-base">
              <p className="text-gray-300">
                <span className="block font-semibold">Phone:</span>
                <a 
                  href="https://wa.me/256753010020" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:text-shannet-yellow transition-colors"
                >
                  +256 (753) 010 020
                </a>
              </p>
              <p className="text-gray-300">
                <span className="block font-semibold">Email:</span>
                <a 
                  href="mailto:<EMAIL>" 
                  className="hover:text-shannet-yellow transition-colors break-all"
                >
                  <EMAIL>
                </a>
              </p>
              <p className="text-gray-300">
                <span className="block font-semibold">Address:</span>
                Kampala<br />
                Uganda<br />
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 md:mt-12 pt-6 md:pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-gray-300 text-xs md:text-sm text-center md:text-left">
        <div className="border-t border-gray-700 mt-8 md:mt-12 pt-6 md:pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-gray-300 text-xs md:text-sm text-center md:text-left">
            © 2024 Shannet Events. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center md:justify-end gap-4 md:gap-6">
            <a href="#" className="text-gray-300 hover:text-white text-xs md:text-sm transition-colors duration-300">
          <div className="flex flex-wrap justify-center md:justify-end gap-4 md:gap-6">
            <a href="#" className="text-gray-300 hover:text-white text-xs md:text-sm transition-colors duration-300">
              Privacy Policy
            </a>
            <a href="#" className="text-gray-300 hover:text-white text-xs md:text-sm transition-colors duration-300">
            <a href="#" className="text-gray-300 hover:text-white text-xs md:text-sm transition-colors duration-300">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
