
import { useState } from "react";
import Gallery from "./Gallery";

const portfolioItems = [
  {
    title: "Wedding Celebrations",
    category: "Wedding",
    image: "/images/shannetw.jpg",
    description: "Beautiful wedding celebration with professional coordination and flawless execution.",
    galleryImages: [
      "/images/shannetw.jpg",
      "/images/shannetw1.jpg",
      "/images/shannetw2.jpg",
      "/images/shannetw6.jpg"
    ]
  },
  {
    title: "Introduction Ceremony",
    category: "Introduction",
    image: "/images/shannetI.jpg",
    description: "Elegant introduction ceremony.",
    galleryImages: [
      "/images/shannetI.jpg",
      "/images/shannetI1.jpg",
    ]
  },
  {
    title: "Will You Marry Me?",
    category: "Proposals",
    image: "/images/shannetP1.jpg",
    description: "Every detail was thoughtfully designed to create an unforgettable memory, marking the beginning of a beautiful new chapter together..",
    galleryImages: [
      "/images/shannetP1.jpg",
      "/images/shannetP2.jpg",
      "/images/shannetP3.jpg",
    ]
  },
  {
    title: "Tech Conference 2022",
    category: "Corporate Event",
    image: "/images/shannetC.jpg",
    description: "Annual technology conference for 500+ attendees with keynote speakers and networking sessions.",
    galleryImages: [
      "/images/shannetC.jpg",
      "/images/shannetC1.jpg",
    ]
  },
  {
    title: "Product Launch Event",
    category: "Product Launch",
    image: "/images/shannetP.jpg",
    description: "High-impact product launch with interactive demonstrations and media coverage.",
    galleryImages: [
      "/images/shannetP.jpg",
    ]
  },
  {
    title: "A Celebration of Love & Joy",
    category: "Bridal Shower",
    image: "/images/shannetS.jpg",
    description: "From heartfelt toasts to playful games and delectable treats, every moment was designed to shower her with happiness as she steps into this beautiful new chapter.",
    galleryImages: [
      "/images/shannetS.jpg",
      "/images/shannetS2.jpg",
    ]
  }
];

const Portfolio = () => {
  const [galleryOpen, setGalleryOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const openGallery = (category: string, images: string[]) => {
    setSelectedCategory(category);
    setSelectedImages(images);
    setGalleryOpen(true);
  };

  const closeGallery = () => {
    setGalleryOpen(false);
    setSelectedCategory("");
    setSelectedImages([]);
  };

  return (
    <section id="portfolio" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-shannet-navy mb-6">
            Our Event Portfolio
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Showcasing our expertise in creating memorable corporate experiences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {portfolioItems.map((item, index) => (
            <div 
              key={index}
              className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 animate-slide-up cursor-pointer"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => openGallery(item.category, item.galleryImages)}
            >
              <div className="aspect-w-16 aspect-h-12 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  loading="lazy"
                  decoding="async"
                />
              </div>
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-shannet-navy/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                  <span className="inline-block px-3 py-1 bg-shannet-yellow text-shannet-navy text-sm font-semibold rounded-full mb-2">
                    {item.category}
                  </span>
                  <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                  <p className="text-gray-200 text-sm">{item.description}</p>
                  <p className="text-shannet-yellow text-sm mt-2 font-semibold">Click to view gallery →</p>
                </div>
              </div>

              {/* Default Content */}
              <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/70 to-transparent text-white group-hover:opacity-0 transition-opacity duration-300">
                <span className="inline-block px-3 py-1 bg-shannet-yellow text-shannet-navy text-sm font-semibold rounded-full mb-2">
                  {item.category}
                </span>
                <h3 className="text-xl font-bold">{item.title}</h3>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 animate-fade-in">
          <div className="bg-shannet-navy rounded-2xl p-8 md:p-12">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Create Your Next Event?
            </h3>
            <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
              Let us help you design and execute an unforgettable corporate experience
            </p>
            <button 
              onClick={() => {
                const element = document.getElementById('booking');
                if (element) element.scrollIntoView({ behavior: 'smooth' });
              }}
              className="bg-shannet-yellow hover:bg-shannet-yellow-dark text-shannet-navy font-bold px-8 py-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Start Planning Today
            </button>
          </div>
        </div>
      </div>

      {/* Gallery Modal */}
      <Gallery
        isOpen={galleryOpen}
        onClose={closeGallery}
        category={selectedCategory}
        images={selectedImages}
      />
    </section>
  );
};

export default Portfolio;
